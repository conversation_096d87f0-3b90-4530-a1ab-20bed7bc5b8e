<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $superAdmin = Role::firstOrCreate(['name' => 'Admin']);
        $student = Role::firstOrCreate(['name' => 'Student']);

        // Assign all existing permissions to Admin
        $superAdmin->syncPermissions(Permission::all());

        // Assign read permissions to Student (exclude sensitive models)
        $excludedFromStudentRead = ['user', 'role', 'permission'];
        $studentPermissions = [];

        // Get all read permissions except excluded ones
        $allPermissions = Permission::where('name', 'like', 'read_%')->get();

        foreach ($allPermissions as $permission) {
            $modelName = str_replace('read_', '', $permission->name);
            if (!in_array($modelName, $excludedFromStudentRead)) {
                $studentPermissions[] = $permission->name;
            }
        }

        // Get permission objects for student
        $studentPermissionObjects = Permission::whereIn('name', $studentPermissions)->get();

        // Assign read permissions to student role
        $student->syncPermissions($studentPermissionObjects);
    }
}

