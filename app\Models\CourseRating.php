<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class CourseRating extends Model
{
    use HasUuids;

    protected $fillable = [
        'course_id',
        'user_id',
        'rating',
        'review'
    ];

    protected $casts = [
        'rating' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function canBeEditedBy(User $user): bool
    {
        return $this->user_id === $user->id || $user->hasRole('Admin');
    }

    public function canBeDeletedBy(User $user): bool
    {
        return $this->user_id === $user->id || $user->hasRole('Admin');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($rating) {
            if (Auth::check()) {
                $rating->user_id = Auth::id();
            }
        });

        static::created(function ($rating) {
            $rating->course->updateRatingStats();
        });

        static::updated(function ($rating) {
            $rating->course->updateRatingStats();
        });

        static::deleted(function ($rating) {
            $rating->course->updateRatingStats();
        });
    }
}