<?php

namespace App\Notifications;

use App\Models\CourseComment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CommentStatusNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $comment;
    protected $status;
    protected $adminName;

    public function __construct(CourseComment $comment, string $status, string $adminName)
    {
        $this->comment = $comment;
        $this->status = $status; // 'approved' or 'rejected'
        $this->adminName = $adminName;
    }

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable)
    {
        $subject = $this->status === 'approved' 
            ? 'Your comment has been approved' 
            : 'Your comment has been rejected';

        $message = $this->status === 'approved'
            ? 'Your comment on the course "' . $this->comment->course->title . '" has been approved and is now visible to other students.'
            : 'Your comment on the course "' . $this->comment->course->title . '" has been rejected and will not be visible to other students.';

        return (new MailMessage)
            ->subject($subject)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line($message)
            ->line('Comment: "' . substr($this->comment->comment, 0, 100) . (strlen($this->comment->comment) > 100 ? '...' : '') . '"')
            ->line('Action taken by: ' . $this->adminName)
            ->action('View Course', url('/courses/' . $this->comment->course->id))
            ->line('Thank you for your participation!');
    }

    public function toArray($notifiable)
    {
        return [
            'title' => $this->status === 'approved' ? 'Comment Approved' : 'Comment Rejected',
            'body' => [
                'message' => $this->status === 'approved' 
                    ? 'Your comment on "' . $this->comment->course->title . '" has been approved.'
                    : 'Your comment on "' . $this->comment->course->title . '" has been rejected.',
                'comment' => substr($this->comment->comment, 0, 100) . (strlen($this->comment->comment) > 100 ? '...' : ''),
                'course_title' => $this->comment->course->title,
                'course_id' => $this->comment->course->id,
                'admin_name' => $this->adminName,
                'status' => $this->status,
                'type' => 'comment_status'
            ]
        ];
    }
}