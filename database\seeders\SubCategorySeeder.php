<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SubCategory;
use App\Models\Category;

class SubCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $subCategories = [
            'Programming' => ['PHP', 'JavaScript', 'Python', 'Java'],
            'Design' => ['UI/UX', 'Graphic Design', 'Animation'],
            'Marketing' => ['SEO', 'Social Media', 'Email Marketing'],
            'Business' => ['Management', 'Entrepreneurship', 'Finance'],
            'Photography' => ['Portrait', 'Landscape', 'Product']
        ];

        foreach ($subCategories as $categoryName => $subs) {
            $category = Category::where('name', $categoryName)->first();
            if (!$category) continue;

            foreach ($subs as $subName) {
                SubCategory::create([
                    'category_id' => $category->id,
                    'name' => $subName,
                    'description' => $subName . ' courses',
                ]);
            }
        }
    }
}
