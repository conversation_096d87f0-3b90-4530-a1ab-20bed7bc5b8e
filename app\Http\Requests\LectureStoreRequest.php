<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Rules\UniqueLectureOrder;

class LectureStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'section_id' => ['required', 'uuid', 'exists:sections,id'],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'order' => [
                'required',
                'integer',
                'min:0',
                new UniqueLectureOrder(0, $this->section_id)
            ],
            'duration' => ['required', 'integer', 'min:1'],
            'image' => ['required', 'image', 'max:2048'],
            'video' => ['nullable', 'file', 'mimes:mp4,mov,avi,wmv', 'max:102400'],
            'lecture_note_file' => ['nullable', 'file', 'mimes:pdf,doc,docx,txt', 'max:51200'],
            'lecture_note' => ['nullable', 'string'],
        ];
    }
}

