<?php

namespace App\Policies;

use App\Models\SubCategory;
use App\Models\User;
use GuzzleHttp\Psr7\Request;
use Illuminate\Auth\Access\Response;

class SubCategoryPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): Response
    {
        return $user->hasPermissionTo('read_sub_category')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, SubCategory $subCategory): Response
    {
        return $user->hasPermissionTo('read_sub_category')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): Response
    {
        return $user->hasPermissionTo('create_sub_category')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, SubCategory $subCategory): Response
    {
        return $user->hasPermissionTo('update_sub_category')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, SubCategory $subCategory): Response
    {
        return $user->hasPermissionTo('delete_sub_category')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, SubCategory $subCategory): Response
    {
        return $user->hasPermissionTo('restore_sub_category')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, SubCategory $subCategory): Response
    {
        return $user->hasPermissionTo('force_delete_sub_category')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }
}
