<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $path = app_path('Models');
        $excludedModels = [];
        $crudPrefixes = ['create', 'read', 'update', 'delete'];

        $files = File::files($path);
        $modelNames = [];

        foreach ($files as $file) {
            $modelName = pathinfo($file->getFilename(), PATHINFO_FILENAME);
            if (in_array($modelName, $excludedModels)) continue;

            $snakeCaseModelName = $this->camelCaseToSnakeCase($modelName);
            $modelNames[] = strtolower($snakeCaseModelName);
        }

        // Create CRUD permissions for each model
        foreach ($modelNames as $modelName) {
            foreach ($crudPrefixes as $prefix) {
                Permission::updateOrCreate(['name' => "{$prefix}_{$modelName}"]);
            }
        }

        // Additional system-wide permissions
        $systemPermissions = [
            'assign_role',
            'attach_permission',
            'detach_permission',
            'read_activity_log',
        ];

        foreach ($systemPermissions as $perm) {
            Permission::updateOrCreate(['name' => $perm]);
        }

        // Assign all permissions to Admin role
        $superAdmin = Role::firstOrCreate(['name' => 'Admin']);
        $superAdmin->syncPermissions(Permission::all());

        // Assign read permissions to existing Student role (exclude sensitive models)
        $student = Role::firstOrCreate(['name' => 'Student']);

        $excludedFromStudentRead = ['user', 'role', 'permission'];
        $studentPermissions = [];

        foreach ($modelNames as $modelName) {
            if (!in_array($modelName, $excludedFromStudentRead)) {
                $studentPermissions[] = "read_{$modelName}";
            }
        }

        // Add custom student actions (if needed)
        $studentPermissions = array_merge($studentPermissions, [
            'attempt_quiz',
            'watch_lecture',
        ]);

        $studentPermissionObjects = Permission::whereIn('name', $studentPermissions)->get();

        if ($studentPermissionObjects->isNotEmpty()) {
            $student->syncPermissions($studentPermissionObjects);
        }
    }

    private  function camelCaseToSnakeCase($input)
    {
        return strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $input));
    }
}
