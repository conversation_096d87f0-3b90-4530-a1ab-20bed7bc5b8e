<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CourseMaterialStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'course_id' => ['required', 'exists:courses,id'],
            'title' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'order' => [
                'nullable',
                'integer',
                Rule::unique('course_materials')->where(fn($query) => $query->where('course_id', $this->course_id)),
            ],
            'file' => ['required', 'file', 'mimes:pdf,docx,mp4,mov', 'max:102400'],
        ];
    }
}
