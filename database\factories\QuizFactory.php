<?php

namespace Database\Factories;

use App\Models\Quiz;
use Illuminate\Database\Eloquent\Factories\Factory;

class QuizFactory extends Factory
{
    protected $model = Quiz::class;

    public function definition(): array
    {
        return [
            'question' => $this->faker->sentence . '?',
            'choice_a' => $this->faker->word,
            'choice_b' => $this->faker->word,
            'choice_c' => $this->faker->word,
            'choice_d' => $this->faker->word,
            'correct_answer' => $this->faker->randomElement(['a', 'b', 'c', 'd']),
        ];
    }
}
