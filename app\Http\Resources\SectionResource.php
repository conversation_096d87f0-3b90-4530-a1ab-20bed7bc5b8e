<?php

namespace App\Http\Resources;

use App\Models\Quiz;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SectionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'course_id' => $this->course_id,
            'title' => $this->title,
            'description' => $this->description,
            'order' => $this->order,
            'quizzes' => $this->quizzes->map(fn($quiz) => [
                'id' => $quiz->id,
                'question' => $quiz->question,
                'choice_a' => $quiz->choice_a,
                'choice_b' => $quiz->choice_b,
                'choice_c' => $quiz->choice_c,
                'choice_d' => $quiz->choice_d,
                'correct_answer' => $quiz->correct_answer,
            ]),
            'lectures' => $this->lectures->map(fn($lecture) => [
                'id' => $lecture->id,
                'title' => $lecture->title,
                'description' => $lecture->description,
                'duration' => $lecture->duration,
                'order' => $lecture->order,
                'lecture_note' => $lecture->lecture_note,
                'lecture_note_file' => $lecture->getFirstMediaUrl('lecture_note_file'),
                'image' => $lecture->getFirstMediaUrl('lecture_image'),
                'video' => $lecture->getFirstMediaUrl('lecture_video'),
            ]),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
