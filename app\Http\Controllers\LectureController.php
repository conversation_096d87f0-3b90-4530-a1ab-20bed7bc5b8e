<?php

namespace App\Http\Controllers;

use App\Http\Requests\LectureStoreRequest;
use App\Http\Resources\LectureResource;
use App\Models\Lecture;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class LectureController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            Gate::authorize('viewAny', Lecture::class);
            $lectures = Lecture::with(['section', 'creator'])->latest()->paginate(10);
            return LectureResource::collection($lectures);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch lectures', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(LectureStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('create', Lecture::class);

            $lecture = Lecture::create($request->validated());

            // Media upload
            if ($request->hasFile('image')) {
                $lecture->addMediaFromRequest('image')->toMediaCollection('lecture_image');
            }
            if ($request->hasFile('video')) {
                $lecture->addMediaFromRequest('video')->toMediaCollection('lecture_video');
            }

            if ($request->hasFile('lecture_note_file')) {
                $lecture->addMediaFromRequest('lecture_note_file')->toMediaCollection('lecture_note_file');
            }

            DB::commit();
            return new LectureResource($lecture->fresh(['section', 'creator']));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to create lecture', 'message' => $e->getMessage()], 500);
        }
    }


    /**
     * Display the specified resource.
     */
    public function show(Lecture $lecture)
    {
        try {
            Gate::authorize('view', $lecture);
            return new LectureResource($lecture->load(['section', 'creator']));
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch lecture', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Lecture $lecture)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('update', $lecture);

            $lecture->update($request->validated());

            // Media update
            if ($request->hasFile('image')) {
                $lecture->clearMediaCollection('lecture_image');
                $lecture->addMediaFromRequest('image')->toMediaCollection('lecture_image');
            }
            if ($request->hasFile('video')) {
                $lecture->clearMediaCollection('lecture_video');
                $lecture->addMediaFromRequest('video')->toMediaCollection('lecture_video');
            }

            DB::commit();
            return new LectureResource($lecture->fresh(['section', 'creator']));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to update lecture', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Lecture $lecture)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('delete', $lecture);

            $lecture->delete();

            DB::commit();
            return response()->json(['message' => 'Lecture deleted successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to delete lecture', 'message' => $e->getMessage()], 500);
        }
    }
}
