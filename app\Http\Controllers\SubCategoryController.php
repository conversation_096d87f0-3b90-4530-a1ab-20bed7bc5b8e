<?php

namespace App\Http\Controllers;

use App\Http\Requests\SubCategoryStoreRequest;
use App\Http\Requests\SubCategoryUpdateRequest;
use App\Http\Resources\SubCategoryResource;
use App\Models\SubCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class SubCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            Gate::authorize('viewAny', SubCategory::class);

            $subCategories = SubCategory::with('category')->latest()->paginate(10);

            return SubCategoryResource::collection($subCategories);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch sub-categories',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(SubCategoryStoreRequest $request)
    {
        DB::beginTransaction();

        try {
            Gate::authorize('create', SubCategory::class);

            $subCategory = SubCategory::create($request->validated());

            DB::commit();

            return new SubCategoryResource($subCategory->load('category'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Failed to create sub-category',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(SubCategory $subCategory)
    {
        try {
            Gate::authorize('view', $subCategory);

            return new SubCategoryResource($subCategory->load('category'));
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch sub-category',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(SubCategoryUpdateRequest $request, SubCategory $subCategory)
    {
        DB::beginTransaction();

        try {
            Gate::authorize('update', $subCategory);

            $subCategory->update($request->validated());

            DB::commit();

            return new SubCategoryResource($subCategory->load('category'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Failed to update sub-category',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SubCategory $subCategory)
    {
        DB::beginTransaction();

        try {
            Gate::authorize('delete', $subCategory);

            $subCategory->delete();

            DB::commit();

            return response()->json(['message' => 'Sub-category deleted successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Failed to delete sub-category',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
