<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CourseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'objectives' => $this->objectives,
            'duration' => $this->duration,
            'welcome_message' => $this->welcome_message,
            'congratulations_message' => $this->congratulations_message,
            'instructor_name' => $this->instructor_name,
            'instructor_description' => $this->instructor_description,
            'instructor_facebook' => $this->instructor_facebook,
            'instructor_instagram' => $this->instructor_instagram,
            'instructor_twitter' => $this->instructor_twitter,
            'image' => $this->getFirstMediaUrl('course_image') ?: null,
            'video' => $this->getFirstMediaUrl('course_trailer') ?: null,
            'average_rating' => $this->average_rating,
            'total_ratings' => $this->total_ratings,
            'enrollments_count' => $this->enrollments_count ?? $this->enrollments()->count(),
            'user_rating' => $this->whenLoaded('userRating'),

            'levels' => $this->levels->map(fn($level) => [
                'id' => $level->id,
                'name' => $level->name,
                'description' => $level->description,
            ]),

            'category' => $this->when($this->category, fn() => [
                'id' => $this->category->id,
                'name' => $this->category->name,
            ]),

            'sub_category' => $this->when($this->subCategory, fn() => [
                'id' => $this->subCategory->id,
                'name' => $this->subCategory->name,
            ]),

            'creator' => $this->when($this->creator, fn() => [
                'id' => $this->creator->id,
                'name' => $this->creator->name,
            ]),

            'sections' => $this->whenLoaded('sections', fn() => $this->sections->map(fn($section) => [
                'id' => $section->id,
                'title' => $section->title,
                'description' => $section->description,
                'order' => $section->order,
                'enrollment_section_id' => $section->enrollment_section_id ?? null,
                'quizzes' => $section->quizzes->map(fn($quiz) => [
                    'id' => $quiz->id,
                    'question' => $quiz->question,
                    'choice_a' => $quiz->choice_a,
                    'choice_b' => $quiz->choice_b,
                    'choice_c' => $quiz->choice_c,
                    'choice_d' => $quiz->choice_d,
                    'correct_answer' => $quiz->correct_answer,
                ]),
                'lectures' => $section->lectures->map(fn($lecture) => [
                    'id' => $lecture->id,
                    'title' => $lecture->title,
                    'description' => $lecture->description,
                    'duration' => $lecture->duration,
                    'order' => $lecture->order,
                    'lecture_note' => $lecture->lecture_note,
                    'lecture_note_file' => $lecture->getFirstMediaUrl('lecture_note_file'),
                    'image' => $lecture->getFirstMediaUrl('lecture_image'),
                    'video' => $lecture->getFirstMediaUrl('lecture_video'),
                    'enrollment_lecture_id' => $lecture->enrollment_lecture_id ?? null,
                ]),
            ])),

            'materials' => $this->whenLoaded('materials', fn() => CourseMaterialResource::collection($this->materials)),
            'lectures_count' => $this->whenLoaded('sections', fn() => $this->sections->sum(fn($section) => $section->lectures->count())),
            'sections_count' => $this->whenLoaded('sections', fn() => $this->sections->count()),
        ];
    }
}
