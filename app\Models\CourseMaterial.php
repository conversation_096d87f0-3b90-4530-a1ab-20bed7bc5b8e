<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class CourseMaterial extends Model implements HasMedia
{
    use HasUuids, InteractsWithMedia, HasFactory;

    protected $fillable = ['course_id', 'title', 'description', 'created_by'];

    protected $appends = ['file_url'];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function getFileUrlAttribute()
    {
        return $this->getFirstMediaUrl('course_materials') ?? null;
    }

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($material) {
            if (Auth::check()) {
                $material->created_by = Auth::id();
            }
        });
    }
}
