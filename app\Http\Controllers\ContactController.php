<?php

namespace App\Http\Controllers;

use App\Mail\ContactNotification;
use App\Models\Contact;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class ContactController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            if (Auth::user()->hasRole('Admin')) {
                $contacts = Contact::latest()->paginate(10);
                return response()->json($contacts);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch contacts', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|max:50',
            'email' => 'required|email',
            'phone' => 'required|starts_with:251|digits:12',
            'program_of_interest' => 'required|min:3|max:50',
            'age'   => 'nullable|integer|min:18|max:80',
            'message' => 'nullable|min:3|max:255',
        ]);

        try {
            Mail::to('<EMAIL>')->send(new ContactNotification($validated));
        } catch (\Exception $e) {
            Log::error('Failed to send contact email: ' . $e->getMessage());
            // Don't throw error - just log it
        }

        $contact = Contact::create($validated);

        return response()->json($contact, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Contact $contact)
    {
        try {
            if (Auth::user()->hasRole('Admin')) {
                return response()->json($contact);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch contact', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Contact $contact) {}

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contact $contact)
    {
        try {
            Gate::authorize('delete', $contact);
            $contact->delete();
            return response()->json(['message' => 'Contact deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to delete contact', 'message' => $e->getMessage()], 500);
        }
    }
}
