<?php

namespace App\Policies;

use App\Models\Quiz;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class QuizPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): Response
    {
        return $user->hasPermissionTo('read_quiz')
            ? Response::allow()
            : Response::deny('You do not have permission to view quize.');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Quiz $quiz): Response
    {
        return $user->hasPermissionTo('read_quiz')
            ? Response::allow()
            : Response::deny('You do not have permission to view quize.');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): Response
    {
        return $user->hasPermissionTo('create_quiz')
            ? Response::allow()
            : Response::deny('You do not have permission to create quize.');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Quiz $quiz): Response
    {
        return $user->hasPermissionTo('update_quiz')
            ? Response::allow()
            : Response::deny('You do not have permission to update quize.');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Quiz $quiz): Response
    {
        return $user->hasPermissionTo('delete_quiz')
            ? Response::allow()
            : Response::deny('You do not have permission to delete quize.');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Quiz $quiz): Response
    {
        return $user->hasPermissionTo('restore_quiz')
            ? Response::allow()
            : Response::deny('You do not have permission to restore quize.');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Quiz $quiz): Response
    {
        return $user->hasPermissionTo('force_delete_quiz')
            ? Response::allow()
            : Response::deny('You do not have permission to force delete quize.');
    }
}
