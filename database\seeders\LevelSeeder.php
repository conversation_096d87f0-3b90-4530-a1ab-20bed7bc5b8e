<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\Level;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // level beginner, intermediate, advanced, professional
        $levels = [
            ['name' => 'Beginner', 'description' => 'Suitable for beginners with no prior experience.'],
            ['name' => 'Intermediate', 'description' => 'For learners with some basic knowledge.'],
            ['name' => 'Advanced', 'description' => 'Designed for experienced learners.'],
            ['name' => 'Professional', 'description' => 'For professionals seeking advanced skills.'],
        ];
        foreach ($levels as $level) {
            Level::updateOrCreate(
                ['name' => $level['name'],  'description' => $level['description']],
            );
        }
    }
}
