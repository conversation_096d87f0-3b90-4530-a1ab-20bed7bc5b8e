<?php

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;


// Add this to routes/web.php for testing
Route::get('/test-email', function () {
    try {
        $testData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '************',
            'program_of_interest' => 'Test Subject',
            'age' => '25',
            'message' => 'This is a test message from the contact form.'
        ];

        Mail::to('<EMAIL>')->send(new App\Mail\ContactNotification($testData));

        return "Test email sent successfully!";
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

Route::get('/', function () {
    return view('welcome');
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
});
