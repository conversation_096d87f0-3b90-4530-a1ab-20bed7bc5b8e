<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\Lecture;

class UniqueLectureOrder implements ValidationRule
{
    private $sectionIndex;
    private $sectionId;

    public function __construct($sectionIndex, $sectionId = null)
    {
        $this->sectionIndex = $sectionIndex;
        $this->sectionId = $sectionId;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // For existing sections, check database
        if ($this->sectionId) {
            $exists = Lecture::where('section_id', $this->sectionId)
                ->where('order', $value)
                ->exists();

            if ($exists) {
                $fail('A lecture with this order already exists in this section.');
            }
        }

        // Check within current request data
        $sections = request()->input('sections', []);

        if (isset($sections[$this->sectionIndex]['lectures'])) {
            $lectures = $sections[$this->sectionIndex]['lectures'];
            $orders = collect($lectures)->pluck('order')->filter()->toArray();

            if (count($orders) !== count(array_unique($orders))) {
                $fail('Lecture orders must be unique within the section.');
            }
        }
    }
}
