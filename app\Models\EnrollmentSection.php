<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

class EnrollmentSection extends Model
{
    use HasUuids;

    protected $fillable = ['enrollment_id', 'section_id', 'status', 'completed_at', 'quiz_completed', 'quiz_score'];

    protected $casts = [
        'completed_at' => 'datetime'
    ];

    public function enrollment()
    {
        return $this->belongsTo(Enrollment::class);
    }

    public function section()
    {
        return $this->belongsTo(Section::class);
    }

    public function quizAttempts()
    {
        return $this->hasMany(QuizAttempt::class);
    }

    public function lectures()
    {
        return $this->hasMany(EnrollmentLecture::class);
    }

    public function isCompleted()
    {
        // Section is completed when all quizzes are attempted (not score-based)
        $sectionQuizzes = $this->section->quizzes;
        if ($sectionQuizzes->count() === 0) {
            return true; // No quizzes means section is automatically completed
        }

        $attemptedQuizzes = $this->quizAttempts()
            ->whereIn('quiz_id', $sectionQuizzes->pluck('id'))
            ->distinct('quiz_id')
            ->count();

        return $attemptedQuizzes >= $sectionQuizzes->count();
    }

    public function markAsCompleted()
    {
        if ($this->isCompleted() && $this->status !== 'completed') {
            $this->update([
                'status' => 'completed',
                'completed_at' => now(),
                'quiz_completed' => true
            ]);
        }
    }

    public function getProgressPercentage()
    {
        $totalLectures = $this->lectures()->count();
        $completedLectures = $this->lectures()->where('status', 'completed')->count();

        return $totalLectures > 0 ? round(($completedLectures / $totalLectures) * 100, 2) : 0;
    }

}



