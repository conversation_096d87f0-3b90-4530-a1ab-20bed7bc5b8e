<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Lecture extends Model implements HasMedia
{
    use HasUuids, InteractsWithMedia, HasFactory;

    protected $fillable = [
        'section_id',
        'title',
        'description',
        'order',
        'duration',
        'lecture_note',
        'created_by'
    ];

    protected $appends = ['image', 'video', 'lecture_note_file_url'];

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function section()
    {
        return $this->belongsTo(Section::class, 'section_id');
    }

    public function quizzes()
    {
        return $this->hasMany(Quiz::class);
    }

    public function getImageAttribute()
    {
        return $this->getFirstMediaUrl('lecture_image') ?? null;
    }


    public function getVideoAttribute()
    {
        return $this->getFirstMediaUrl('lecture_video') ?? null;
    }

    public function getLectureNoteFileUrlAttribute()
    {
        return $this->getFirstMediaUrl('lecture_note_file') ?? null;
    }

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($lecture) {
            if (Auth::check()) {
                $lecture->created_by = Auth::id();
            }
        });
    }
}
