<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LectureUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $lectureId = $this->route('lecture')->id;

        return [
            'section_id' => ['required', 'exists:sections,id'],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'order' => [
                'required',
                'integer',
                Rule::unique('lectures')
                    ->where(fn($query) => $query->where('section_id', $this->section_id))
                    ->ignore($lectureId),
            ],
            'duration' => ['required', 'integer', 'min:1'],
            'image' => ['nullable', 'image', 'max:2048'],
            'video' => ['nullable', 'file', 'mimes:mp4,mov,avi,wmv', 'max:102400'],
        ];
    }
}
