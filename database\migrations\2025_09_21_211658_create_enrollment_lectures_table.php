<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enrollment_lectures', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('enrollment_section_id');
            $table->uuid('lecture_id');
            $table->enum('status', ['locked', 'unlocked', 'completed'])->default('locked');
            $table->boolean('video_watched')->default(false);
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            $table->foreign('enrollment_section_id')->references('id')->on('enrollment_sections')->onDelete('cascade');
            $table->foreign('lecture_id')->references('id')->on('lectures')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enrollment_lectures');
    }
};
