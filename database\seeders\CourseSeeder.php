<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\{Course, CourseMaterial, Section, Lecture, Quiz, Level};
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;

class CourseSeeder extends Seeder
{
    public function run(): void
    {
        DB::transaction(function () {
            // Define paths to your seed files
            $seedFilesPath = public_path('seed-files');
            $imageFile = "{$seedFilesPath}/kp.jpg";
            $videoFile = "{$seedFilesPath}/spa.mp4";
            $pdfFile = "{$seedFilesPath}/final-document.pdf"; // Corrected filename with space

            // Debug: Print paths and check existence
            dump([
                'imageFile' => $imageFile,
                'videoFile' => $videoFile,
                'pdfFile' => $pdfFile,
                'imageExists' => file_exists($imageFile),
                'videoExists' => file_exists($videoFile),
                'pdfExists' => file_exists($pdfFile),
                'directoryContents' => scandir($seedFilesPath),
            ]);

            // Verify files exist
            if (!file_exists($imageFile) || !file_exists($videoFile) || !file_exists($pdfFile)) {
                throw new \Exception('Seed files (kp.jpg, spa.mp4, final-document .pdf) not found in storage/app/seed-files');
            }

            // Create 5 courses
            Course::factory()->count(5)->create()->each(function ($course) use ($imageFile, $videoFile, $pdfFile) {
                // Attach random levels
                $levels = Level::inRandomOrder()->take(rand(1, 3))->pluck('id');
                $course->levels()->attach($levels);

                // Add course media using real files
                $course->addMedia($imageFile)->preservingOriginal()->toMediaCollection('course_image');
                $course->addMedia($videoFile)->preservingOriginal()->toMediaCollection('course_trailer');

                // Create Sections with quizzes
                $sectionIndex = 0;
                $course->sections()->createMany(
                    Section::factory()->count(5)->make()->map(function ($section) use (&$sectionIndex) {
                        return collect($section->getAttributes())
                            ->merge([
                                'order' => ++$sectionIndex,
                            ])->only(['title', 'description', 'order'])->toArray();
                    })->toArray()
                );

                // Create Lectures and Quizzes for each section
                $course->sections->each(function ($section) use ($imageFile, $videoFile, $pdfFile) {
                    // Create Lectures
                    $lectureIndex = 0;
                    Lecture::factory()->count(5)->make()->each(function ($lecture) use ($section, &$lectureIndex, $imageFile, $videoFile, $pdfFile) {
                        $lectureData = collect($lecture->getAttributes())
                            ->except(['image', 'video', 'lecture_note_file_url'])
                            ->merge([
                                'order' => ++$lectureIndex,
                            ])
                            ->toArray();

                        $lecture = $section->lectures()->create($lectureData);

                        // Add lecture media using real files
                        $lecture->addMedia($imageFile)->preservingOriginal()->toMediaCollection('lecture_image');
                        $lecture->addMedia($videoFile)->preservingOriginal()->toMediaCollection('lecture_video');
                        $lecture->addMedia($pdfFile)->preservingOriginal()->toMediaCollection('lecture_note_file');
                    });

                    // Create Quizzes for section
                    Quiz::factory()->count(5)->create([
                        'section_id' => $section->id
                    ]);
                });

                // Create Course Materials (2 per course)
                $materialsData = CourseMaterial::factory()->count(2)->make()
                    ->map(function ($material) {
                        return collect($material->getAttributes())
                            ->except(['file_url'])
                            ->toArray();
                    })
                    ->toArray();

                $materials = $course->materials()->createMany($materialsData);

                // Add real PDF file to materials
                $materials->each(function ($material) use ($pdfFile) {
                    $material->addMedia($pdfFile)->preservingOriginal()->toMediaCollection('course_materials');
                });
            });
        });
    }
}
