<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdminDashboardResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'overview' => $this->resource['overview'],
            'user_analytics' => $this->resource['user_analytics'],
            'course_analytics' => $this->resource['course_analytics'],
            'enrollment_analytics' => $this->resource['enrollment_analytics'],
            'performance_metrics' => $this->resource['performance_metrics'],
            'recent_activities' => $this->resource['recent_activities'],
            'top_performers' => $this->resource['top_performers'],
            'system_health' => $this->resource['system_health'],
            'generated_at' => now()->toISOString(),
        ];
    }
}