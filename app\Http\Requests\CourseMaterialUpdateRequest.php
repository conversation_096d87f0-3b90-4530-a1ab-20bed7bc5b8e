<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CourseMaterialUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $materialId = $this->route('course_material')->id;

        return [
            'course_id' => ['required', 'exists:courses,id'],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'order' => [
                'required',
                'integer',
                Rule::unique('course_materials')
                    ->where(fn($query) => $query->where('course_id', $this->course_id))
                    ->ignore($materialId),
            ],
            'file' => ['nullable', 'file', 'mimes:pdf,docx,mp4,mov', 'max:102400'],
        ];
    }
}
