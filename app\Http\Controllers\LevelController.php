<?php

namespace App\Http\Controllers;

use App\Http\Requests\LevelStoreRequest;
use App\Http\Requests\LevelUpdateRequest;
use App\Http\Resources\LevelResource;
use App\Models\Level;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

class LevelController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            Gate::authorize('viewAny', Level::class);
            $levels = Level::latest()->paginate(10);
            return LevelResource::collection($levels);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch levels', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(LevelStoreRequest $request)
    {
        try {
            Gate::authorize('create', Level::class);
            $level = Level::create($request->validated());
            return new LevelResource($level);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to create level', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Level $level)
    {
        try {
            Gate::authorize('view', $level);
            return new LevelResource($level);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch level', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(LevelUpdateRequest $request, Level $level)
    {
        try {
            Gate::authorize('update', $level);
            $level->update($request->validated());
            return new LevelResource($level);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to update level', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Level $level)
    {
        try {
            Gate::authorize('delete', $level);
            $level->delete();
            return response()->json(['message' => 'Level deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to delete level', 'message' => $e->getMessage()], 500);
        }
    }
}
