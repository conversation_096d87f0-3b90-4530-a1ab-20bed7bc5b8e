<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

class Enrollment extends Model
{
    use HasUuids;

    protected $fillable = ['course_id', 'user_id', 'status', 'progress', 'completed_at'];

    protected $casts = [
        'progress' => 'float',
        'completed_at' => 'datetime'
    ];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function sections()
    {
        return $this->hasMany(EnrollmentSection::class);
    }

    public function calculateProgress()
    {
        $totalLectures = $this->sections()->withCount('lectures')->get()->sum('lectures_count');
        $completedLectures = EnrollmentLecture::whereHas('section', function($query) {
            $query->where('enrollment_id', $this->id);
        })->where('status', 'completed')->count();

        $progress = $totalLectures > 0 ? round(($completedLectures / $totalLectures) * 100, 2) : 0;

        $this->update(['progress' => $progress]);

        return $progress;
    }

    public function isCompleted()
    {
        return $this->sections()->where('status', '!=', 'completed')->count() === 0;
    }

    public function markAsCompleted()
    {
        if ($this->isCompleted() && $this->status !== 'completed') {
            $this->update([
                'status' => 'completed',
                'completed_at' => now(),
                'progress' => 100
            ]);
        }
    }

    public function progressBySection($sectionId)
    {
        $section = $this->sections()->where('section_id', $sectionId)->first();
        return $section ? $section->status : null;
    }
}

