{"title": "Complete Web Development Bootcamp", "description": "Master modern web development from frontend to backend with hands-on projects and real-world applications.", "objectives": "Build responsive websites, create REST APIs, work with databases, deploy applications, and understand modern development workflows.", "category_id": "", "sub_category_id": "", "duration": 120, "welcome_message": "Welcome to the Complete Web Development Bootcamp! Get ready to transform your coding skills.", "congratulations_message": "Congratulations! You've completed the bootcamp and are now ready to build amazing web applications.", "instructor_name": "<PERSON>", "instructor_description": "Senior Full Stack Developer with 8+ years of experience in web development and teaching.", "instructor_facebook": "https://facebook.com/johnsmith", "instructor_instagram": "https://instagram.com/johnsmith_dev", "instructor_twitter": "https://twitter.com/johnsmith_dev", "levels": [], "sections": [{"title": "Frontend Fundamentals", "description": "Learn the core technologies for building user interfaces", "order": 1, "lectures": [{"title": "HTML Basics and Structure", "description": "Understanding HTML elements, attributes, and document structure", "order": 1, "duration": 45, "lecture_note": "HTML is the foundation of web development. Focus on semantic markup and accessibility.", "quizzes": [{"question": "What does HTML stand for?", "choice_a": "Hyper Text Markup Language", "choice_b": "High Tech Modern Language", "choice_c": "Home Tool Markup Language", "choice_d": "Hyperlink and Text Markup Language", "correct_answer": "a"}, {"question": "Which HTML element is used for the largest heading?", "choice_a": "<h6>", "choice_b": "<h1>", "choice_c": "<header>", "choice_d": "<heading>", "correct_answer": "b"}, {"question": "What is the correct HTML element for inserting a line break?", "choice_a": "<break>", "choice_b": "<lb>", "choice_c": "<br>", "choice_d": "<newline>", "correct_answer": "c"}, {"question": "Which attribute specifies the URL of the page the link goes to?", "choice_a": "link", "choice_b": "src", "choice_c": "href", "choice_d": "url", "correct_answer": "c"}, {"question": "What is the correct HTML for creating a hyperlink?", "choice_a": "<a url='http://example.com'>Example</a>", "choice_b": "<a href='http://example.com'>Example</a>", "choice_c": "<a>http://example.com</a>", "choice_d": "<link>http://example.com</link>", "correct_answer": "b"}]}, {"title": "CSS Styling and Layout", "description": "Master CSS for styling and creating responsive layouts", "order": 2, "duration": 60, "lecture_note": "CSS controls the presentation of HTML. Learn selectors, properties, and layout techniques.", "quizzes": [{"question": "What does CSS stand for?", "choice_a": "Creative Style Sheets", "choice_b": "Cascading Style Sheets", "choice_c": "Computer Style Sheets", "choice_d": "Colorful Style Sheets", "correct_answer": "b"}, {"question": "Which CSS property is used to change the text color?", "choice_a": "text-color", "choice_b": "font-color", "choice_c": "color", "choice_d": "text-style", "correct_answer": "c"}, {"question": "How do you select an element with id 'header' in CSS?", "choice_a": ".header", "choice_b": "#header", "choice_c": "*header", "choice_d": "header", "correct_answer": "b"}, {"question": "Which CSS property controls the text size?", "choice_a": "font-style", "choice_b": "text-size", "choice_c": "font-size", "choice_d": "text-style", "correct_answer": "c"}, {"question": "What is the default value of the position property?", "choice_a": "relative", "choice_b": "absolute", "choice_c": "fixed", "choice_d": "static", "correct_answer": "d"}]}, {"title": "JavaScript Fundamentals", "description": "Learn JavaScript basics including variables, functions, and DOM manipulation", "order": 3, "duration": 75, "lecture_note": "JavaScript brings interactivity to web pages. Focus on ES6+ features and best practices.", "quizzes": [{"question": "Which of the following is the correct way to declare a variable in JavaScript?", "choice_a": "var myVar;", "choice_b": "variable myVar;", "choice_c": "v myVar;", "choice_d": "declare myVar;", "correct_answer": "a"}, {"question": "What is the correct way to write a JavaScript array?", "choice_a": "var colors = 'red', 'green', 'blue'", "choice_b": "var colors = ['red', 'green', 'blue']", "choice_c": "var colors = (1:'red', 2:'green', 3:'blue')", "choice_d": "var colors = 1 = ('red'), 2 = ('green'), 3 = ('blue')", "correct_answer": "b"}, {"question": "How do you write 'Hello World' in an alert box?", "choice_a": "alertBox('Hello World');", "choice_b": "msg('Hello World');", "choice_c": "alert('Hello World');", "choice_d": "msgBox('Hello World');", "correct_answer": "c"}, {"question": "Which operator is used to assign a value to a variable?", "choice_a": "*", "choice_b": "=", "choice_c": "-", "choice_d": "x", "correct_answer": "b"}, {"question": "What will the following code return: <PERSON><PERSON><PERSON>(10 > 9)", "choice_a": "true", "choice_b": "false", "choice_c": "NaN", "choice_d": "undefined", "correct_answer": "a"}]}, {"title": "Responsive Web Design", "description": "Create websites that work on all devices using modern CSS techniques", "order": 4, "duration": 50, "lecture_note": "Responsive design ensures your website looks great on all screen sizes. Master flexbox and grid.", "quizzes": [{"question": "What is the viewport meta tag used for?", "choice_a": "Setting the page title", "choice_b": "Controlling the page's dimensions and scaling", "choice_c": "Adding keywords for SEO", "choice_d": "Setting the page description", "correct_answer": "b"}, {"question": "Which CSS unit is relative to the viewport width?", "choice_a": "px", "choice_b": "em", "choice_c": "vw", "choice_d": "pt", "correct_answer": "c"}, {"question": "What does 'mobile-first' design approach mean?", "choice_a": "Designing only for mobile devices", "choice_b": "Starting with mobile design and scaling up", "choice_c": "Making mobile version after desktop", "choice_d": "Using only mobile-specific features", "correct_answer": "b"}, {"question": "Which CSS property is used to create flexible layouts?", "choice_a": "display: block", "choice_b": "display: inline", "choice_c": "display: flex", "choice_d": "display: none", "correct_answer": "c"}, {"question": "What is a breakpoint in responsive design?", "choice_a": "A point where the website breaks", "choice_b": "A screen width where layout changes", "choice_c": "An error in the code", "choice_d": "A pause in development", "correct_answer": "b"}]}, {"title": "Frontend Frameworks Introduction", "description": "Overview of popular frontend frameworks and when to use them", "order": 5, "duration": 40, "lecture_note": "Frontend frameworks speed up development. Learn when and why to use React, Vue, or Angular.", "quizzes": [{"question": "Which of the following is a JavaScript framework?", "choice_a": "HTML", "choice_b": "CSS", "choice_c": "React", "choice_d": "PHP", "correct_answer": "c"}, {"question": "What is the Virtual DOM?", "choice_a": "A physical representation of the DOM", "choice_b": "A JavaScript representation of the real DOM", "choice_c": "A CSS framework", "choice_d": "A database technology", "correct_answer": "b"}, {"question": "Which company developed React?", "choice_a": "Google", "choice_b": "Microsoft", "choice_c": "Facebook", "choice_d": "Apple", "correct_answer": "c"}, {"question": "What is JSX?", "choice_a": "A database query language", "choice_b": "A CSS preprocessor", "choice_c": "A syntax extension for JavaScript", "choice_d": "A server-side language", "correct_answer": "c"}, {"question": "Which framework uses TypeScript by default?", "choice_a": "React", "choice_b": "Vue.js", "choice_c": "Angular", "choice_d": "j<PERSON><PERSON><PERSON>", "correct_answer": "c"}]}]}, {"title": "Backend Development", "description": "Server-side programming, databases, and API development", "order": 2, "lectures": [{"title": "Server-Side Programming Basics", "description": "Understanding server-side concepts and choosing the right technology", "order": 1, "duration": 55, "lecture_note": "Server-side programming handles business logic, data processing, and database interactions.", "quizzes": [{"question": "What is server-side programming?", "choice_a": "Programming that runs in the browser", "choice_b": "Programming that runs on the server", "choice_c": "Programming for mobile apps", "choice_d": "Programming for desktop applications", "correct_answer": "b"}, {"question": "Which of the following is a server-side language?", "choice_a": "HTML", "choice_b": "CSS", "choice_c": "JavaScript (client-side)", "choice_d": "PHP", "correct_answer": "d"}, {"question": "What does API stand for?", "choice_a": "Application Programming Interface", "choice_b": "Advanced Programming Interface", "choice_c": "Automated Programming Interface", "choice_d": "Application Process Interface", "correct_answer": "a"}, {"question": "Which HTTP method is used to retrieve data?", "choice_a": "POST", "choice_b": "PUT", "choice_c": "GET", "choice_d": "DELETE", "correct_answer": "c"}, {"question": "What is the purpose of a web server?", "choice_a": "To store files only", "choice_b": "To serve web pages and handle requests", "choice_c": "To design websites", "choice_d": "To write code", "correct_answer": "b"}]}, {"title": "Database Design and SQL", "description": "Learn database concepts, design principles, and SQL queries", "order": 2, "duration": 70, "lecture_note": "Databases store and organize data efficiently. Master SQL for data manipulation and retrieval.", "quizzes": [{"question": "What does SQL stand for?", "choice_a": "Structured Query Language", "choice_b": "Simple Query Language", "choice_c": "Standard Query Language", "choice_d": "System Query Language", "correct_answer": "a"}, {"question": "Which SQL statement is used to extract data from a database?", "choice_a": "EXTRACT", "choice_b": "SELECT", "choice_c": "GET", "choice_d": "OPEN", "correct_answer": "b"}, {"question": "What is a primary key?", "choice_a": "The first column in a table", "choice_b": "A unique identifier for each record", "choice_c": "The most important data", "choice_d": "A password for the database", "correct_answer": "b"}, {"question": "Which SQL clause is used to filter records?", "choice_a": "FILTER", "choice_b": "WHERE", "choice_c": "HAVING", "choice_d": "SELECT", "correct_answer": "b"}, {"question": "What is normalization in database design?", "choice_a": "Making all data the same format", "choice_b": "Organizing data to reduce redundancy", "choice_c": "Sorting data alphabetically", "choice_d": "Backing up the database", "correct_answer": "b"}]}, {"title": "RESTful API Development", "description": "Build robust APIs following REST principles and best practices", "order": 3, "duration": 65, "lecture_note": "REST APIs enable communication between different applications. Follow REST principles for scalable APIs.", "quizzes": [{"question": "What does REST stand for?", "choice_a": "Representational State Transfer", "choice_b": "Remote State Transfer", "choice_c": "Relational State Transfer", "choice_d": "Responsive State Transfer", "correct_answer": "a"}, {"question": "Which HTTP status code indicates a successful request?", "choice_a": "404", "choice_b": "500", "choice_c": "200", "choice_d": "301", "correct_answer": "c"}, {"question": "What is the purpose of HTTP methods in REST?", "choice_a": "To style the API", "choice_b": "To indicate the desired action", "choice_c": "To secure the API", "choice_d": "To format the response", "correct_answer": "b"}, {"question": "Which format is commonly used for API responses?", "choice_a": "XML only", "choice_b": "HTML", "choice_c": "JSON", "choice_d": "Plain text", "correct_answer": "c"}, {"question": "What is endpoint in API context?", "choice_a": "The end of the program", "choice_b": "A URL where API can be accessed", "choice_c": "The final result", "choice_d": "An error message", "correct_answer": "b"}]}, {"title": "Authentication and Security", "description": "Implement secure authentication systems and protect against common vulnerabilities", "order": 4, "duration": 60, "lecture_note": "Security is crucial in web development. Learn authentication, authorization, and common security practices.", "quizzes": [{"question": "What is the difference between authentication and authorization?", "choice_a": "They are the same thing", "choice_b": "Authentication verifies identity, authorization grants access", "choice_c": "Authorization verifies identity, authentication grants access", "choice_d": "Authentication is for APIs, authorization is for websites", "correct_answer": "b"}, {"question": "What does JWT stand for?", "choice_a": "Java Web Token", "choice_b": "JavaScript Web Token", "choice_c": "JSON Web Token", "choice_d": "Just Web Token", "correct_answer": "c"}, {"question": "Which is a common method to store passwords securely?", "choice_a": "Plain text", "choice_b": "Base64 encoding", "choice_c": "Hashing with salt", "choice_d": "Reversible encryption", "correct_answer": "c"}, {"question": "What is HTTPS?", "choice_a": "HTTP with security", "choice_b": "HTTP over SSL/TLS", "choice_c": "A new version of HTTP", "choice_d": "HTTP for mobile devices", "correct_answer": "b"}, {"question": "What is SQL injection?", "choice_a": "A way to optimize SQL queries", "choice_b": "A security vulnerability in database queries", "choice_c": "A method to backup databases", "choice_d": "A SQL debugging technique", "correct_answer": "b"}]}, {"title": "Server Deployment and DevOps", "description": "Deploy applications to production servers and understand DevOps practices", "order": 5, "duration": 45, "lecture_note": "Deployment gets your application live. Learn about servers, cloud platforms, and deployment strategies.", "quizzes": [{"question": "What is deployment in web development?", "choice_a": "Writing code", "choice_b": "Testing the application", "choice_c": "Making the application available to users", "choice_d": "Designing the user interface", "correct_answer": "c"}, {"question": "Which of the following is a cloud platform?", "choice_a": "HTML", "choice_b": "CSS", "choice_c": "AWS", "choice_d": "JavaScript", "correct_answer": "c"}, {"question": "What is CI/CD?", "choice_a": "Code Integration/Code Deployment", "choice_b": "Continuous Integration/Continuous Deployment", "choice_c": "Client Integration/Client Deployment", "choice_d": "Creative Integration/Creative Deployment", "correct_answer": "b"}, {"question": "What is a domain name?", "choice_a": "The name of the developer", "choice_b": "A human-readable address for a website", "choice_c": "The programming language used", "choice_d": "The database name", "correct_answer": "b"}, {"question": "What is the purpose of a CDN?", "choice_a": "To write code faster", "choice_b": "To deliver content from servers closer to users", "choice_c": "To design better interfaces", "choice_d": "To manage databases", "correct_answer": "b"}]}]}, {"title": "Full-Stack Projects", "description": "Build complete web applications combining frontend and backend technologies", "order": 3, "lectures": [{"title": "Project Planning and Architecture", "description": "Learn how to plan and architect full-stack applications", "order": 1, "duration": 50, "lecture_note": "Good planning prevents poor performance. Learn to design scalable application architecture.", "quizzes": [{"question": "What is the first step in project planning?", "choice_a": "Writing code", "choice_b": "Defining requirements", "choice_c": "Choosing technologies", "choice_d": "Designing the UI", "correct_answer": "b"}, {"question": "What is MVC architecture?", "choice_a": "Model-View-Controller", "choice_b": "Multiple-View-Control", "choice_c": "Modern-Visual-Code", "choice_d": "Mobile-Virtual-Computer", "correct_answer": "a"}, {"question": "What is a wireframe?", "choice_a": "A type of cable", "choice_b": "A basic visual guide of a page layout", "choice_c": "A programming framework", "choice_d": "A database schema", "correct_answer": "b"}, {"question": "What does scalability mean in web development?", "choice_a": "Making the website bigger", "choice_b": "The ability to handle increased load", "choice_c": "Adding more features", "choice_d": "Making the code longer", "correct_answer": "b"}, {"question": "What is a user story?", "choice_a": "A biography of the user", "choice_b": "A short description of a feature from user's perspective", "choice_c": "A long documentation", "choice_d": "A test case", "correct_answer": "b"}]}, {"title": "Building a Todo Application", "description": "Create a full-stack todo application with CRUD operations", "order": 2, "duration": 90, "lecture_note": "Todo apps are perfect for learning CRUD operations. Focus on clean code and user experience.", "quizzes": [{"question": "What does CRUD stand for?", "choice_a": "Create, Read, Update, Delete", "choice_b": "<PERSON><PERSON>, <PERSON>, Update, Delete", "choice_c": "Create, <PERSON>move, Update, Delete", "choice_d": "Create, Read, Upload, Delete", "correct_answer": "a"}, {"question": "Which HTTP method is typically used for updating data?", "choice_a": "GET", "choice_b": "POST", "choice_c": "PUT", "choice_d": "DELETE", "correct_answer": "c"}, {"question": "What is state management in frontend applications?", "choice_a": "Managing the application's data and UI state", "choice_b": "Managing server states", "choice_c": "Managing file states", "choice_d": "Managing user states", "correct_answer": "a"}, {"question": "What is the purpose of form validation?", "choice_a": "To make forms look better", "choice_b": "To ensure data quality and security", "choice_c": "To speed up the application", "choice_d": "To reduce server load", "correct_answer": "b"}, {"question": "What is localStorage in web browsers?", "choice_a": "A server-side storage", "choice_b": "A client-side storage mechanism", "choice_c": "A database type", "choice_d": "A cloud storage service", "correct_answer": "b"}]}, {"title": "E-commerce Platform Development", "description": "Build a complete e-commerce platform with payment integration", "order": 3, "duration": 120, "lecture_note": "E-commerce platforms require complex features like user management, inventory, and payments.", "quizzes": [{"question": "What is a shopping cart in e-commerce?", "choice_a": "A physical cart", "choice_b": "A temporary storage for selected items", "choice_c": "A payment method", "choice_d": "A delivery method", "correct_answer": "b"}, {"question": "What is inventory management?", "choice_a": "Managing customer data", "choice_b": "Managing product stock and availability", "choice_c": "Managing website design", "choice_d": "Managing user accounts", "correct_answer": "b"}, {"question": "What is a payment gateway?", "choice_a": "A physical gate for payments", "choice_b": "A service that processes online payments", "choice_c": "A type of database", "choice_d": "A security feature", "correct_answer": "b"}, {"question": "What is SSL certificate used for?", "choice_a": "To make websites faster", "choice_b": "To encrypt data transmission", "choice_c": "To improve SEO", "choice_d": "To add animations", "correct_answer": "b"}, {"question": "What is order fulfillment?", "choice_a": "Taking customer orders", "choice_b": "The process of completing and delivering orders", "choice_c": "Canceling orders", "choice_d": "Refunding orders", "correct_answer": "b"}]}, {"title": "Social Media Dashboard", "description": "Create a social media dashboard with real-time features", "order": 4, "duration": 100, "lecture_note": "Social media apps require real-time updates, user interactions, and content management.", "quizzes": [{"question": "What is real-time communication in web applications?", "choice_a": "Very fast HTTP requests", "choice_b": "Instant data exchange between client and server", "choice_c": "Scheduled data updates", "choice_d": "Cached data responses", "correct_answer": "b"}, {"question": "What is WebSocket?", "choice_a": "A type of web server", "choice_b": "A protocol for real-time communication", "choice_c": "A JavaScript framework", "choice_d": "A database technology", "correct_answer": "b"}, {"question": "What is a news feed algorithm?", "choice_a": "A way to create news", "choice_b": "A method to determine what content to show users", "choice_c": "A way to write articles", "choice_d": "A method to delete posts", "correct_answer": "b"}, {"question": "What is content moderation?", "choice_a": "Creating content", "choice_b": "Reviewing and filtering user-generated content", "choice_c": "Deleting all content", "choice_d": "Promoting content", "correct_answer": "b"}, {"question": "What is infinite scrolling?", "choice_a": "A scrolling animation", "choice_b": "Loading content continuously as user scrolls", "choice_c": "A very long webpage", "choice_d": "A type of navigation", "correct_answer": "b"}]}, {"title": "Portfolio Website and Deployment", "description": "Build and deploy a professional portfolio website", "order": 5, "duration": 80, "lecture_note": "A portfolio showcases your skills and projects. Make it responsive, fast, and professional.", "quizzes": [{"question": "What is the main purpose of a portfolio website?", "choice_a": "To sell products", "choice_b": "To showcase skills and work", "choice_c": "To provide news", "choice_d": "To entertain visitors", "correct_answer": "b"}, {"question": "What is SEO?", "choice_a": "Search Engine Optimization", "choice_b": "Social Engine Optimization", "choice_c": "Server Engine Optimization", "choice_d": "Site Engine Optimization", "correct_answer": "a"}, {"question": "What is the purpose of meta tags?", "choice_a": "To style the webpage", "choice_b": "To provide metadata about the webpage", "choice_c": "To add interactivity", "choice_d": "To store user data", "correct_answer": "b"}, {"question": "What is website performance optimization?", "choice_a": "Making the website look better", "choice_b": "Improving website speed and efficiency", "choice_c": "Adding more features", "choice_d": "Increasing website size", "correct_answer": "b"}, {"question": "What is a custom domain?", "choice_a": "A domain created by the user", "choice_b": "A personalized web address for your site", "choice_c": "A type of hosting", "choice_d": "A programming language", "correct_answer": "b"}]}]}], "materials": [{"title": "Web Development Cheat Sheet", "description": "Quick reference guide for HTML, CSS, and JavaScript"}, {"title": "Project Templates and Boilerplates", "description": "Starter templates for common web development projects"}, {"title": "Resource Links and Documentation", "description": "Curated list of useful resources and official documentation"}]}