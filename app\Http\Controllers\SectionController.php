<?php

namespace App\Http\Controllers;

use App\Http\Requests\SectionStoreRequest;
use App\Http\Requests\SectionUpdateRequest;
use App\Http\Resources\SectionResource;
use App\Models\Section;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class SectionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            Gate::authorize('viewAny', Section::class);
            $sections = Section::with('course')->latest()->paginate(10);
            return SectionResource::collection($sections);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch sections', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(SectionStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('create', Section::class);

            $section = Section::create($request->validated());

            // Optional media uploads
            if ($request->hasFile('image')) {
                $section->addMediaFromRequest('image')->toMediaCollection('section_image');
            }
            if ($request->hasFile('video')) {
                $section->addMediaFromRequest('video')->toMediaCollection('section_video');
            }

            DB::commit();
            return new SectionResource($section->fresh('course'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error'   => 'Failed to create section',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Section $section)
    {
        try {
            Gate::authorize('view', $section);
            return new SectionResource($section->load('course'));
        } catch (\Exception $e) {
            return response()->json([
                'error'   => 'Failed to fetch section',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(SectionUpdateRequest $request, Section $section)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('update', $section);

            $section->update($request->validated());

            // Optional media updates
            if ($request->hasFile('image')) {
                $section->clearMediaCollection('section_image');
                $section->addMediaFromRequest('image')->toMediaCollection('section_image');
            }
            if ($request->hasFile('video')) {
                $section->clearMediaCollection('section_video');
                $section->addMediaFromRequest('video')->toMediaCollection('section_video');
            }

            DB::commit();
            return new SectionResource($section->fresh('course'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error'   => 'Failed to update section',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Section $section)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('delete', $section);

            $section->delete();

            DB::commit();
            return response()->json(['message' => 'Section deleted successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error'   => 'Failed to delete section',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
