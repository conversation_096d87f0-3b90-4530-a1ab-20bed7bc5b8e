<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CourseUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        return [
            'title' => [
                'required',
                'string',
                'max:255',
                Rule::unique('courses', 'title')->ignore($this->route('course')->id),
            ],
            'description' => 'nullable|string',
            'objectives' => 'nullable|array',
            'objectives.*' => 'string|max:500',
            'category_id' => 'required|exists:categories,id',
            'sub_category_id' => 'nullable|exists:sub_categories,id',
            'duration' => 'nullable|integer|min:1',
            'levels' => 'nullable|array',
            'levels.*' => 'exists:levels,id',
            'image' => 'nullable|image|max:2048',
            'video' => 'nullable|file|mimes:mp4,mov,avi,wmv|max:20480',

            // Instructor / welcome fields
            'welcome_message' => 'nullable|string',
            'congratulations_message' => 'nullable|string',
            'instructor_name' => 'nullable|string|max:255',
            'instructor_description' => 'nullable|string',
            'instructor_facebook' => 'nullable|url',
            'instructor_instagram' => 'nullable|url',
            'instructor_twitter' => 'nullable|url',
        ];
    }
}
