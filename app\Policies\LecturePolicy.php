<?php

namespace App\Policies;

use App\Models\Lecture;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class LecturePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): Response
    {
        return $user->hasPermissionTo('read_lecture')
            ? Response::allow()
            : Response::deny('You do not have permission to view Lecture.');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Lecture $lecture): Response
    {
        return $user->hasPermissionTo('read_lecture')
            ? Response::allow()
            : Response::deny('You do not have permission to view Lecture.');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): Response
    {
        return $user->hasPermissionTo('create_lecture')
            ? Response::allow()
            : Response::deny('You do not have permission to create Lecture.');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Lecture $lecture): Response
    {
        return $user->hasPermissionTo('update_lecture')
            ? Response::allow()
            : Response::deny('You do not have permission to update Lecture.');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Lecture $lecture): Response
    {
        return $user->hasPermissionTo('delete_lecture')
            ? Response::allow()
            : Response::deny('You do not have permission to delete Lecture.');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Lecture $lecture): Response
    {
        return $user->hasPermissionTo('restore_lecture')
            ? Response::allow()
            : Response::deny('You do not have permission to restore Lecture.');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Lecture $lecture): Response
    {
        return $user->hasPermissionTo('force_delete_lecture')
            ? Response::allow()
            : Response::deny('You do not have permission to force delete Lecture.');
    }
}
