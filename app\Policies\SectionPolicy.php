<?php

namespace App\Policies;

use App\Models\Section;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class SectionPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): Response
    {
        return $user->hasPermissionTo('read_section')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Section $section): Response
    {
        return $user->hasPermissionTo('read_section')
            ? Response::allow()
            : Response::deny('You do not have permission to view this section.');
    }
    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): Response
    {
        return $user->hasPermissionTo('create_section')
            ? Response::allow()
            : Response::deny('You do not have permission to create sections.');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Section $section): Response
    {
        return $user->hasPermissionTo('update_section')
            ? Response::allow()
            : Response::deny('You do not have permission to update this section.');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Section $section): Response
    {
        return $user->hasPermissionTo('delete_section')
            ? Response::allow()
            : Response::deny('You do not have permission to delete this section.');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Section $section): Response
    {
        return $user->hasPermissionTo('restore_section')
            ? Response::allow()
            : Response::deny('You do not have permission to restore this section.');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Section $section): Response
    {
        return $user->hasPermissionTo('force_delete_section')
            ? Response::allow()
            : Response::deny('You do not have permission to permanently delete this section.');
    }
}
