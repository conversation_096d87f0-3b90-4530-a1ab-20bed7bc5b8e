<?php

namespace App\Policies;

use App\Models\CourseMaterial;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class CourseMaterialPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): Response
    {
        return $user->hasPermissionTo('read_course_material')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, CourseMaterial $courseMaterial): Response
    {
        return $user->hasPermissionTo('read_course_material')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): Response
    {
        return $user->hasPermissionTo('create_course_material')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CourseMaterial $courseMaterial): Response
    {
        return $user->hasPermissionTo('update_course_material')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CourseMaterial $courseMaterial): Response
    {
        return $user->hasPermissionTo('delete_course_material')
            ? Response::allow()
            : Response::deny('You do not have permission to view sections.');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, CourseMaterial $courseMaterial): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, CourseMaterial $courseMaterial): bool
    {
        return false;
    }
}
