<?php

namespace App\Http\Controllers;

use App\Http\Resources\CourseResource;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\EnrollmentLecture;
use App\Models\EnrollmentSection;
use App\Models\Quiz;
use App\Models\QuizAttempt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EnrollmentController extends Controller
{
    public function enroll(Request $request)
    {
        $data = $request->validate([
            'course_id' => 'required|exists:courses,id'
        ]);

        $user = $request->user();

        // Check if already enrolled
        $existingEnrollment = Enrollment::where('course_id', $data['course_id'])
            ->where('user_id', $user->id)
            ->first();

        if ($existingEnrollment) {
            return response()->json(['message' => 'Already enrolled in this course'], 409);
        }

        DB::beginTransaction();
        try {
            $enrollment = Enrollment::create([
                'course_id' => $data['course_id'],
                'user_id' => $user->id,
                'status' => 'active',
                'progress' => 0
            ]);

            $course = Course::with('sections.lectures')->find($data['course_id']);

            foreach ($course->sections as $section) {
                $enrollmentSection = $enrollment->sections()->create([
                    'section_id' => $section->id,
                    'status' => 'unlocked',
                    'quiz_completed' => false,
                    'quiz_score' => 0
                ]);

                foreach ($section->lectures as $lecture) {
                    $enrollmentSection->lectures()->create([
                        'lecture_id' => $lecture->id,
                        'status' => 'unlocked',
                        'video_watched' => false,
                        'quiz_completed' => false,
                    ]);
                }
            }

            DB::commit();
            return response()->json(['message' => 'Enrolled successfully', 'enrollment' => $enrollment]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Enrollment failed'], 500);
        }
    }

    public function watchVideo(Request $request, $enrollmentLectureId)
    {
        $enrollmentLecture = EnrollmentLecture::findOrFail($enrollmentLectureId);

        // check if video watched is already true and status is completed
        if ($enrollmentLecture->video_watched && $enrollmentLecture->status === 'completed') {
            return response()->json(['message' => 'Video already watched and lecture completed'], 200);
        }
        // Mark video as watched and lecture as completed
        $enrollmentLecture->update([
            'video_watched' => true,
            'status' => 'completed',
            'completed_at' => now()
        ]);

        return response()->json(['message' => 'Video watched and lecture completed', 'enrollment_lecture' => $enrollmentLecture->fresh()]);
    }

    public function completeQuiz(Request $request, $enrollmentSectionId)
    {
        $request->validate([
            'quiz_id' => 'required|exists:quizzes,id',
            'selected_answer' => 'required|in:a,b,c,d'
        ]);

        $enrollmentSection = EnrollmentSection::findOrFail($enrollmentSectionId);

        // if ($enrollmentSection->status !== 'unlocked') {
        //     return response()->json(['error' => 'Section is not unlocked'], 403);
        // }

        // Get the quiz to check correct answer
        $quiz = Quiz::findOrFail($request->quiz_id);
        $isCorrect = $quiz->correct_answer === $request->selected_answer;

        // Record quiz attempt (for record keeping only)
        $quizAttempt = $enrollmentSection->quizAttempts()->create([
            'quiz_id' => $request->quiz_id,
            'user_id' => $request->user()->id,
            'selected_answer' => $request->selected_answer,
            'is_correct' => $isCorrect,
            'score' => $isCorrect ? 100 : 0
        ]);

        // Check if all quizzes for this section have been attempted (not score-based)
        $sectionQuizzes = $enrollmentSection->section->quizzes;
        $attemptedQuizzes = $enrollmentSection->quizAttempts()
            ->whereIn('quiz_id', $sectionQuizzes->pluck('id'))
            ->distinct('quiz_id')
            ->count();

        $allQuizzesAttempted = $attemptedQuizzes >= $sectionQuizzes->count();

        if ($allQuizzesAttempted) {
            // Calculate final score for record keeping
            $correctAnswers = $enrollmentSection->quizAttempts()
                ->whereIn('quiz_id', $sectionQuizzes->pluck('id'))
                ->where('is_correct', true)
                ->count();

            $totalQuestions = $sectionQuizzes->count();
            $finalScore = $totalQuestions > 0 ? round(($correctAnswers / $totalQuestions) * 100, 2) : 0;

            // Mark section as completed (attempt-based, not score-based)
            $enrollmentSection->update([
                'status' => 'completed',
                'quiz_completed' => true,
                'quiz_score' => $finalScore,
                'completed_at' => now()
            ]);


            // Update enrollment progress
            $enrollmentSection->enrollment->calculateProgress();

            // Check if enrollment is completed
            if ($enrollmentSection->enrollment->isCompleted()) {
                $enrollmentSection->enrollment->markAsCompleted();
            }

            return response()->json([
                'message' => 'All quizzes attempted! Section completed.',
                'quiz_attempt' => $quizAttempt,
                'is_correct' => $isCorrect,
                'section_completed' => true,
                'final_results' => [
                    'correct_answers' => $correctAnswers,
                    'total_questions' => $totalQuestions,
                    'score' => $finalScore,
                    'percentage' => "{$correctAnswers}/{$totalQuestions}"
                ],
                'section' => $enrollmentSection->fresh()
            ]);
        }

        return response()->json([
            'message' => 'Quiz answer submitted',
            'quiz_attempt' => $quizAttempt,
            'is_correct' => $isCorrect,
            'correct_answer' => $isCorrect ? null : $quiz->correct_answer,
            'section_completed' => false,
            'progress' => [
                'attempted' => $attemptedQuizzes,
                'total' => $sectionQuizzes->count()
            ]
        ]);
    }

    // protected function unlockNextLecture(EnrollmentLecture $completedLecture)
    // {
    //     $section = $completedLecture->section;

    //     // Find next lecture in same section
    //     // $nextLecture = $section->lectures()
    //     //     ->where('status', 'locked')
    //     //     ->whereHas('lecture', function ($query) use ($completedLecture) {
    //     //         $query->where('order', '>', $completedLecture->lecture->order);
    //     //     })
    //     //     ->orderBy('created_at')
    //     //     ->first();

    //     $nextLecture = $section->lectures()
    //         ->where('status', 'locked')
    //         ->where('order', '>', $completedLecture->lecture->order)
    //         ->orderBy('order', 'asc')
    //         ->first();


    //     if ($nextLecture) {
    //         // Unlock next lecture in same section
    //         $nextLecture->update(['status' => 'unlocked']);
    //     }

    //     // Update enrollment progress
    //     $section->enrollment->calculateProgress();
    // }

    // protected function unlockNextSection($completedSection)
    // {
    //     $enrollment = $completedSection->enrollment;

    //     // $nextSection = $enrollment->sections()
    //     //     ->where('status', 'locked')
    //     //     ->whereHas('section', function ($query) use ($completedSection) {
    //     //         $query->where('order', '>', $completedSection->section->order);
    //     //     })
    //     //     ->orderBy('created_at')
    //     //     ->first();

    //     $nextSection = $enrollment->sections()
    //         ->where('status', 'locked')
    //         ->where('order', '>', $completedSection->order) // direct order check
    //         ->orderBy('order', 'asc') // ensure next by order
    //         ->first();

    //     if ($nextSection) {
    //         $nextSection->update(['status' => 'unlocked']);

    //         // Unlock first lecture in next section
    //         // $firstLecture = $nextSection->lectures()
    //         //     ->orderBy('created_at')
    //         //     ->first();
    //         $firstLecture = $nextSection->lectures()
    //             ->where('status', 'locked')
    //             ->orderBy('order', 'asc')
    //             ->first();

    //         if ($firstLecture) {
    //             $firstLecture->update(['status' => 'unlocked']);
    //         }
    //     }
    // }

    public function getProgress($enrollmentId)
    {
        $enrollment = Enrollment::with([
            'course',
            'sections.section',
            'sections.lectures.lecture'
        ])->findOrFail($enrollmentId);

        $progress = $enrollment->calculateProgress();

        return response()->json([
            'progress' => $progress,
        ]);
    }

    public function myEnrollments(Request $request)
    {
        // add search functionality by course title and category name
        $request->validate([
            'search' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);
        $enrollments = Enrollment::with([
            'course:id,title,average_rating,total_ratings,category_id,sub_category_id',
            'course.category:id,name',
            'course.subCategory:id,name',
        ])
            ->where('user_id', $request->user()->id)
            ->when($request->search, function ($query, $search) {
                $query->whereHas('course', function ($query) use ($search) {
                    $query->where('title', 'like', "%{$search}%")
                        ->orWhereHas('category', function ($query) use ($search) {
                            $query->where('name', 'like', "%{$search}%");
                        });
                });
            })
            ->latest()
            ->paginate($request->per_page ?? 9);

        return response()->json([
            'data' => $enrollments->map(function ($enrollment) {
                return [
                    'enrolement_id' => $enrollment->id,
                    'status' => $enrollment->status,
                    'progress' => $enrollment->calculateProgress(),
                    'course' => [
                        'id' => $enrollment->course->id,
                        'title' => $enrollment->course->title,
                        'image' => $enrollment->course->image,
                        'average_rating' => $enrollment->course->average_rating,
                        'total_ratings' => $enrollment->course->total_ratings,
                        'enrollments_count' => $enrollment->course->enrollments()->count(),
                        'category' => $enrollment->course->category->name ?? null,
                        'sub_category' => $enrollment->course->subCategory->name ?? null,
                    ],
                ];
            }),
            'pagination' => [
                'current_page' => $enrollments->currentPage(),
                'per_page' => $enrollments->perPage(),
                'total' => $enrollments->total(),
                'last_page' => $enrollments->lastPage(),
                'from' => $enrollments->firstItem(),
                'to' => $enrollments->lastItem(),
                'next_page_url' => $enrollments->nextPageUrl(),
                'prev_page_url' => $enrollments->previousPageUrl(),
                'path' => $enrollments->path(),
                'links' => $enrollments->linkCollection(),
                'has_next' => $enrollments->hasMorePages(),
                'has_previous' => $enrollments->currentPage() > 1,
            ]
        ]);
    }

    public function unenroll(Request $request, $enrollmentId)
    {
        $enrollment = Enrollment::where('id', $enrollmentId)
            ->where('user_id', $request->user()->id)
            ->firstOrFail();

        $enrollment->update(['status' => 'dropped']);

        return response()->json(['message' => 'Successfully unenrolled from course']);
    }

    public function getLecture($enrollmentLectureId)
    {
        $enrollmentLecture = EnrollmentLecture::with([
            'section.section',
            'section.enrollment.course'
        ])->findOrFail($enrollmentLectureId);

        return response()->json([
            'lecture' => $enrollmentLecture,
            'can_access' => $enrollmentLecture->status === 'unlocked' || $enrollmentLecture->status === 'completed'
        ]);
    }

    public function getCourseMaterials($enrollmentId)
    {
        $enrollment = Enrollment::with('course.materials')->findOrFail($enrollmentId);

        return response()->json($enrollment->course->materials);
    }

    public function getSectionQuizAttempts($enrollmentSectionId)
    {
        $attempts = QuizAttempt::where('enrollment_section_id', $enrollmentSectionId)
            ->with('quiz')
            ->latest()
            ->get();

        return response()->json($attempts);
    }

    public function getSectionQuizResults($enrollmentSectionId)
    {
        $enrollmentSection = EnrollmentSection::with(['section.quizzes', 'quizAttempts.quiz'])
            ->findOrFail($enrollmentSectionId);

        $sectionQuizzes = $enrollmentSection->section->quizzes;
        $attempts = $enrollmentSection->quizAttempts;

        $results = $sectionQuizzes->map(function ($quiz) use ($attempts) {
            $attempt = $attempts->where('quiz_id', $quiz->id)->first();

            return [
                'quiz_id' => $quiz->id,
                'question' => $quiz->question,
                'choices' => [
                    'a' => $quiz->choice_a,
                    'b' => $quiz->choice_b,
                    'c' => $quiz->choice_c,
                    'd' => $quiz->choice_d,
                ],
                'correct_answer' => $quiz->correct_answer,
                'user_answer' => $attempt ? $attempt->selected_answer : null,
                'is_correct' => $attempt ? $attempt->is_correct : null,
                'attempted' => $attempt !== null,
                'attempted_at' => $attempt ? $attempt->created_at : null,
            ];
        });

        $totalQuestions = $sectionQuizzes->count();
        $attemptedQuestions = $attempts->count();
        $correctAnswers = $attempts->where('is_correct', true)->count();
        $finalScore = $totalQuestions > 0 ? round(($correctAnswers / $totalQuestions) * 100, 2) : 0;

        return response()->json([
            'section_id' => $enrollmentSection->section_id,
            'section_title' => $enrollmentSection->section->title,
            'quiz_results' => $results,
            'summary' => [
                'total_questions' => $totalQuestions,
                'attempted_questions' => $attemptedQuestions,
                'correct_answers' => $correctAnswers,
                'score' => $finalScore,
                'percentage' => "{$correctAnswers}/{$totalQuestions}",
                'section_completed' => $enrollmentSection->status === 'completed',
                'all_attempted' => $attemptedQuestions >= $totalQuestions
            ]
        ]);
    }

    public function getDashboardStats(Request $request)
    {
        $user = $request->user();

        $stats = [
            'total_enrollments' => Enrollment::where('user_id', $user->id)->count(),
            'completed_courses' => Enrollment::where('user_id', $user->id)->where('status', 'completed')->count(),
            'in_progress_courses' => Enrollment::where('user_id', $user->id)->where('status', 'active')->count(),
            'total_lectures_completed' => EnrollmentLecture::whereHas('section', function ($query) use ($user) {
                $query->whereHas('enrollment', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                });
            })->where('status', 'completed')->count(),
            'average_progress' => Enrollment::where('user_id', $user->id)->avg('progress') ?? 0
        ];

        return response()->json($stats);
    }

    public function getRecentActivity(Request $request)
    {
        $recentLectures = EnrollmentLecture::whereHas('section', function ($query) use ($request) {
            $query->whereHas('enrollment', function ($q) use ($request) {
                $q->where('user_id', $request->user()->id);
            });
        })
            ->where('status', 'completed')
            ->with(['lecture', 'section.section', 'section.enrollment.course'])
            ->latest('completed_at')
            ->limit(10)
            ->get();

        return response()->json(
            $recentLectures->map(function ($lecture) {
                return [
                    'id' => $lecture->id,
                    'lecture_title' => $lecture->lecture->title,
                    'section_title' => $lecture->section->section->title,
                    'course_title' => $lecture->section->enrollment->course->title,
                    'completed_at' => $lecture->completed_at,
                ];
            })
        );
    }


    public function getCourseByEnrollmentId($enrollmentId)
    {
        try {
            // Get the enrollment with course and necessary relations
            $enrollment = Enrollment::with([
                'course.levels',
                'course.category',
                'course.subCategory',
                'course.creator',
                'course.sections.lectures',
                'course.sections.quizzes',
                'course.materials',
                'sections.lectures',
            ])->findOrFail($enrollmentId);

            $course = $enrollment->course;

            // add enrollment lecture id and enrollment section id to each lecture and section
            foreach ($course->sections as $section) {
                $enrollmentSection = $enrollment->sections->firstWhere('section_id', $section->id);
                $section->enrollment_section_id = $enrollmentSection ? $enrollmentSection->id : null;

                foreach ($section->lectures as $lecture) {
                    $enrollmentLecture = null;
                    if ($enrollmentSection) {
                        $enrollmentLecture = $enrollmentSection->lectures->firstWhere('lecture_id', $lecture->id);
                    }
                    $lecture->enrollment_lecture_id = $enrollmentLecture ? $enrollmentLecture->id : null;
                }
            }


            $totalSections = $course->sections->count();
            $totalLectures = $course->sections->sum(fn($section) => $section->lectures->count());

            $courseResource = new CourseResource($course);
            $additionalData = [
                'total_sections' => $totalSections,
                'total_lectures' => $totalLectures,
            ];

            return $courseResource->additional($additionalData);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch course by enrollment ID',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
