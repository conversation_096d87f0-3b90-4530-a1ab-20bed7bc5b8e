<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class QuizRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'section_id' => ['required', 'exists:sections,id'],
            'question' => ['required', 'string'],
            'choice_a' => ['required', 'string'],
            'choice_b' => ['required', 'string'],
            'choice_c' => ['required', 'string'],
            'choice_d' => ['required', 'string'],
            'correct_answer' => ['required', 'in:a,b,c,d'],
        ];
    }
}

