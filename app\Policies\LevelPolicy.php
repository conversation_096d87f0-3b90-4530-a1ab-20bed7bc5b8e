<?php

namespace App\Policies;

use App\Models\Level;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class LevelPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): Response
    {
        return $user->hasPermissionTo('read_level')
            ? Response::allow()
            : Response::deny('You do not have permission to view levels.');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Level $level): Response
    {
        return $user->hasPermissionTo('read_level')
            ? Response::allow()
            : Response::deny('You do not have permission to view this level.');
    }


    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): Response
    {
        return $user->hasPermissionTo('create_level')
            ? Response::allow()
            : Response::deny('You do not have permission to create levels.');
    }


    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Level $level): Response
    {
        return $user->hasPermissionTo('update_level')
            ? Response::allow()
            : Response::deny('You do not have permission to update this level.');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Level $level): Response
    {
        return $user->hasPermissionTo('delete_level')
            ? Response::allow()
            : Response::deny('You do not have permission to delete this level.');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Level $level): Response
    {
        return $user->hasPermissionTo('restore_level')
            ? Response::allow()
            : Response::deny('You do not have permission to restore this level.');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Level $level): Response
    {
        return $user->hasPermissionTo('force_delete_level')
            ? Response::allow()
            : Response::deny('You do not have permission to permanently delete this level.');
    }
}
