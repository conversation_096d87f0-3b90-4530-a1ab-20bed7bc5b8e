<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Laravel\Scout\Searchable;

class Course extends Model implements HasMedia
{
    use HasUuids, InteractsWithMedia, Searchable, HasFactory;

    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'objectives' => $this->objectives,
            'instructor_name' => $this->instructor_name,
            'category_name' => $this->category?->name,
            'sub_category_name' => $this->subCategory?->name,
            'level_names' => $this->levels->pluck('name')->implode(' '),
        ];
    }

    public function shouldBeSearchable(): bool
    {
        return true;
    }

    protected $fillable = [
        'category_id',
        'sub_category_id',
        'title',
        'description',
        'objectives',
        'created_by',
        'duration',
        'welcome_message',
        'congratulations_message',
        'instructor_name',
        'instructor_description',
        'instructor_facebook',
        'instructor_instagram',
        'instructor_twitter',
        'status',
        'average_rating',
        'total_ratings',
    ];

    protected $casts = [
        'objectives' => 'json',
    ];

    protected $appends = ['image', 'video'];
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function subCategory()
    {
        return $this->belongsTo(SubCategory::class);
    }

    public function levels()
    {
        return $this->belongsToMany(Level::class, 'course_level', 'course_id', 'level_id');
    }

    public function sections()
    {
        return $this->hasMany(Section::class);
    }

    public function materials()
    {
        return $this->hasMany(CourseMaterial::class);
    }


    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function getImageAttribute()
    {
        return $this->getFirstMediaUrl('course_image') ?? null;
    }

    public function getVideoAttribute()
    {
        return $this->getFirstMediaUrl('course_trailer') ?? null;
    }

    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    public function activeEnrollments()
    {
        return $this->hasMany(Enrollment::class)->where('status', 'active');
    }

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($course) {
            if (Auth::check()) {
                $course->created_by = Auth::id();
            }
        });
    }

    public function comments()
    {
        return $this->hasMany(CourseComment::class)->with('user', 'approvedReplies');
    }

    public function approvedComments()
    {
        return $this->hasMany(CourseComment::class)
            ->where('is_approved', true)
            ->whereNull('parent_id')
            ->with('user', 'approvedReplies')
            ->latest();
    }

    public function ratings()
    {
        return $this->hasMany(CourseRating::class);
    }

    public function userRating()
    {
        return $this->hasOne(CourseRating::class)->where('user_id', Auth::id());
    }

    public function updateRatingStats()
    {
        $ratings = $this->ratings();
        $totalRatings = $ratings->count();
        $averageRating = $totalRatings > 0 ? $ratings->avg('rating') : 0;

        $this->update([
            'total_ratings' => $totalRatings,
            'average_rating' => round($averageRating, 2)
        ]);
    }

    public function getRatingDistribution()
    {
        return $this->ratings()
            ->selectRaw('rating, COUNT(*) as count')
            ->groupBy('rating')
            ->orderBy('rating', 'desc')
            ->pluck('count', 'rating')
            ->toArray();
    }

    public function isEnrolled()
    {
        if (!Auth::check()) {
            return false;
        }

        return $this->enrollments()
            ->where('user_id', Auth::id())
            ->where('status', 'active')
            ->exists();
    }

    // if isEnrollment true return enrollment Id
    public function getEnrollmentId()
    {
        if (!Auth::check()) {
            return null;
        }

        return $this->enrollments()
            ->where('user_id', Auth::id())
            ->where('status', 'active')
            ->first()->id ?? null;
    }
}
