<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\CourseRating;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CourseRatingController extends Controller
{
    public function index(Request $request, Course $course)
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100',
            'rating_filter' => 'nullable|integer|min:1|max:5',
            'sort_by' => 'nullable|in:rating,created_at',
            'sort_order' => 'nullable|in:asc,desc'
        ]);

        $perPage = $request->per_page ?? 15;
        $sortBy = $request->sort_by ?? 'created_at';
        $sortOrder = $request->sort_order ?? 'desc';

        $query = $course->ratings()
            ->with(['user:id,full_name,email'])
            ->when($request->rating_filter, function ($query, $rating) {
                return $query->where('rating', $rating);
            });

        $ratings = $query->orderBy($sortBy, $sortOrder)->paginate($perPage);

        // Add rating distribution and stats
        $ratingStats = [
            'average_rating' => $course->average_rating,
            'total_ratings' => $course->total_ratings,
            'distribution' => $course->getRatingDistribution(),
            'user_rating' => $course->userRating
        ];

        return response()->json([
            'ratings' => $ratings,
            'stats' => $ratingStats
        ]);
    }

    public function store(Request $request, Course $course)
    {
        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:1000'
        ]);

        $user = Auth::user();

        // Check if user is enrolled (optional requirement)
        if (!$user->hasRole('Admin')) {
            $isEnrolled = $user->enrollments()->where('course_id', $course->id)->exists();
            if (!$isEnrolled) {
                return response()->json(['message' => 'You must be enrolled in this course to rate it'], 403);
            }
        }

        // Check if user already rated this course
        $existingRating = CourseRating::where('course_id', $course->id)
            ->where('user_id', $user->id)
            ->first();

        if ($existingRating) {
            return response()->json(['message' => 'You have already rated this course. Use PUT to update your rating.'], 409);
        }

        $rating = CourseRating::create([
            'course_id' => $course->id,
            'rating' => $request->rating,
            'review' => $request->review
        ]);

        $rating->load(['user:id,full_name,email']);

        return response()->json([
            'message' => 'Rating added successfully',
            'rating' => $rating
        ], 201);
    }

    public function show(CourseRating $rating)
    {
        $rating->load(['user:id,full_name,email', 'course:id,title']);

        return response()->json($rating);
    }

    public function update(Request $request, CourseRating $rating)
    {
        $user = Auth::user();

        if (!$rating->canBeEditedBy($user)) {
            return response()->json(['message' => 'Unauthorized to edit this rating'], 403);
        }

        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:1000'
        ]);

        $rating->update([
            'rating' => $request->rating,
            'review' => $request->review
        ]);

        $rating->load(['user:id,full_name,email']);

        return response()->json([
            'message' => 'Rating updated successfully',
            'rating' => $rating
        ]);
    }

    public function destroy(CourseRating $rating)
    {
        $user = Auth::user();

        if (!$rating->canBeDeletedBy($user)) {
            return response()->json(['message' => 'Unauthorized to delete this rating'], 403);
        }

        $rating->delete();

        return response()->json(['message' => 'Rating deleted successfully']);
    }

    public function getUserRating(Course $course)
    {
        $user = Auth::user();

        $rating = CourseRating::where('course_id', $course->id)
            ->where('user_id', $user->id)
            ->first();

        if (!$rating) {
            return response()->json(['message' => 'No rating found'], 404);
        }

        return response()->json($rating);
    }

    public function upsertRating(Request $request, Course $course)
    {
        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:1000'
        ]);

        $user = Auth::user();

        // Check enrollment
        if (!$user->hasRole('Admin')) {
            $isEnrolled = $user->enrollments()->where('course_id', $course->id)->exists();
            if (!$isEnrolled) {
                return response()->json(['message' => 'You must be enrolled in this course to rate it'], 403);
            }
        }

        $rating = CourseRating::updateOrCreate(
            [
                'course_id' => $course->id,
                'user_id' => $user->id
            ],
            [
                'rating' => $request->rating,
                'review' => $request->review
            ]
        );

        $rating->load(['user:id,full_name,email']);

        return response()->json([
            'message' => $rating->wasRecentlyCreated ? 'Rating added successfully' : 'Rating updated successfully',
            'rating' => $rating,
            'was_created' => $rating->wasRecentlyCreated
        ]);
    }

    public function getRatingStats(Course $course)
    {
        $stats = [
            'average_rating' => $course->average_rating,
            'total_ratings' => $course->total_ratings,
            'distribution' => $course->getRatingDistribution(),
            'percentage_distribution' => []
        ];

        // Calculate percentage distribution
        if ($course->total_ratings > 0) {
            foreach (range(1, 5) as $star) {
                $count = $stats['distribution'][$star] ?? 0;
                $stats['percentage_distribution'][$star] = round(($count / $course->total_ratings) * 100, 1);
            }
        }

        return response()->json($stats);
    }

    public function getUserRatings(Request $request)
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100'
        ]);

        $perPage = $request->per_page ?? 15;
        $user = Auth::user();

        $ratings = $user->courseRatings()
            ->with(['course:id,title'])
            ->latest()
            ->paginate($perPage);

        return response()->json($ratings);
    }
}
