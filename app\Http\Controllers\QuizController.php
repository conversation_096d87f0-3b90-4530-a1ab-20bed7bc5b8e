<?php

namespace App\Http\Controllers;

use App\Http\Requests\QuizRequest;
use App\Http\Resources\QuizResource;
use App\Models\Quiz;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class QuizController extends Controller
{
    public function index()
    {
        Gate::authorize('viewAny', Quiz::class);
        return QuizResource::collection(Quiz::latest()->get());
    }

    public function store(QuizRequest $request)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('create', Quiz::class);
            $quiz = Quiz::create($request->validated());
            DB::commit();
            return new QuizResource($quiz);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to create quiz', 'message' => $e->getMessage()], 500);
        }
    }

    public function show(Quiz $quiz)
    {
        Gate::authorize('view', $quiz);
        return new QuizResource($quiz);
    }

    public function update(QuizRequest $request, Quiz $quiz)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('update', $quiz);
            $quiz->update($request->validated());
            DB::commit();
            return new QuizResource($quiz);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to update quiz', 'message' => $e->getMessage()], 500);
        }
    }

    public function destroy(Quiz $quiz)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('delete', $quiz);
            $quiz->delete();
            DB::commit();
            return response()->json(['message' => 'Quiz deleted successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to delete quiz', 'message' => $e->getMessage()], 500);
        }
    }

    public function getSectionQuiz($sectionId)
    {
        try {
            $quizzes = Quiz::where('section_id', $sectionId)->get();

            if ($quizzes->isEmpty()) {
                return response()->json(['message' => 'No quizzes found for this section'], 404);
            }

            return QuizResource::collection($quizzes);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch section quiz', 'message' => $e->getMessage()], 500);
        }
    }
}

