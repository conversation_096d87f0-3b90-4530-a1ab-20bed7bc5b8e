<?php

namespace Database\Factories;

use App\Models\Course;
use App\Models\Category;
use App\Models\SubCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CourseFactory extends Factory
{
    protected $model = Course::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence,
            'description' => $this->faker->paragraph,
            'objectives' => $this->faker->sentence,
            'category_id' => Category::inRandomOrder()->value('id'),
            'sub_category_id' => SubCategory::inRandomOrder()->value('id'), // UUID supported
            'duration' => $this->faker->numberBetween(60, 300),
            'created_by' => User::first()->id,
            'welcome_message' => $this->faker->sentence,
            'congratulations_message' => $this->faker->sentence,
            'instructor_name' => $this->faker->name,
            'instructor_description' => $this->faker->paragraph,
            'instructor_facebook' => $this->faker->url,
            'instructor_instagram' => $this->faker->url,
            'instructor_twitter' => $this->faker->url,
        ];
    }
}
