<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CourseWizardModifiedRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $step = $this->input('step');

        switch ($step) {
            case 1:
                return [
                    'step' => ['required', 'integer'],
                    'course_id' => ['nullable', 'exists:courses,id'],
                    'title' => [
                        'required',
                        'string',
                        'max:255',
                        Rule::unique('courses', 'title')->ignore($this->course_id),
                    ],
                    'category_id' => 'required|exists:categories,id',
                    'sub_category_id' => 'nullable|exists:sub_categories,id',
                    'duration' => 'nullable|integer|min:1',
                    'levels' => 'nullable|array',
                    'levels.*' => 'exists:levels,id',
                ];
            case 2:
                return [
                    'step' => ['required', 'integer'],
                    'course_id' => ['required', 'exists:courses,id'],
                    'description' => 'nullable|string',
                    'objectives' => 'nullable|array',
                    'objectives.*' => 'string|max:500',
                    'instructor_name' => 'nullable|string|max:255',
                    'instructor_description' => 'nullable|string',
                    'instructor_facebook' => 'nullable|url',
                    'instructor_instagram' => 'nullable|url',
                    'image' => 'nullable|file|mimes:jpg,jpeg,png,gif,mp4,mov|max:10240', // 10MB max
                    'video' => 'nullable|file|mimes:mp4,mov,avi|max:51200', // 50MB max
                ];
            case 3:
                return [
                    'step' => ['required', 'integer'],
                    'course_id' => ['required', 'exists:courses,id'],
                    'sections' => ['required', 'array'],
                    'sections.*.title' => ['required', 'string'],
                    'sections.*.description' => ['nullable', 'string'],
                    'sections.*.order' => ['required', 'integer'],
                    'sections.*.lectures' => ['nullable', 'array'],
                    'sections.*.lectures.*.title' => ['required', 'string'],
                    'sections.*.lectures.*.description' => ['nullable', 'string'],
                    'sections.*.lectures.*.order' => ['required', 'integer'],
                    'sections.*.lectures.*.duration' => ['nullable', 'integer'],
                    'sections.*.quizzes' => ['nullable', 'array'],
                    'sections.*.quizzes.*.question' => ['required', 'string'],
                    'sections.*.quizzes.*.choice_a' => ['required', 'string'],
                    'sections.*.quizzes.*.choice_b' => ['required', 'string'],
                    'sections.*.quizzes.*.choice_c' => ['required', 'string'],
                    'sections.*.quizzes.*.choice_d' => ['required', 'string'],
                    'sections.*.quizzes.*.correct_answer' => ['required', 'in:a,b,c,d'],
                ];
            case 4:
                return [
                    'step' => ['required', 'integer'],
                    'course_id' => ['required', 'exists:courses,id'],
                    'welcome_message' => 'nullable|string',
                    'congratulations_message' => 'nullable|string',
                    'materials' => 'nullable|array',
                    'materials.*.title' => 'nullable|string|max:255',
                    'materials.*.description' => 'nullable|string',
                    'materials.*.file' => 'nullable|file|mimes:pdf,doc,docx,ppt,pptx,zip|max:51200',
                ];
        }

        // Default return if step does not match any case
        return [
            'step' => ['required', 'integer'],
            'course_id' => ['required', 'exists:courses,id'],
        ];
    }

    public function messages()
    {
        return [
            'sections.*.lectures.*.quizzes.*.correct_answer.in' => 'The correct answer must be one of: a, b, c, d.',
            'sections.*.lectures.*.quizzes.*.correct_answer.required' => 'The correct answer is required.',
            'title.required' => 'The course title is required.',
            'title.unique' => 'The course title has already been taken.',
            'category_id.required' => 'The course category is required.',
        ];
    }
}
