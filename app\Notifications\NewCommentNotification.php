<?php

namespace App\Notifications;

use App\Models\CourseComment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewCommentNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $comment;

    public function __construct(CourseComment $comment)
    {
        $this->comment = $comment;
    }

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('New Comment Awaiting Approval')
            ->greeting('Hello Admin!')
            ->line('A new comment has been posted and is awaiting your approval.')
            ->line('Course: ' . $this->comment->course->title)
            ->line('Student: ' . $this->comment->user->name)
            ->line('Comment: "' . substr($this->comment->comment, 0, 150) . (strlen($this->comment->comment) > 150 ? '...' : '') . '"')
            ->action('Review Comment', url('/admin/comments/pending'))
            ->line('Please review and approve or reject this comment.');
    }

    public function toArray($notifiable)
    {
        return [
            'title' => 'New Comment Pending Approval',
            'body' => [
                'message' => 'A new comment from ' . $this->comment->user->name . ' is awaiting approval.',
                'comment' => substr($this->comment->comment, 0, 100) . (strlen($this->comment->comment) > 100 ? '...' : ''),
                'course_title' => $this->comment->course->title,
                'course_id' => $this->comment->course->id,
                'student_name' => $this->comment->user->name,
                'comment_id' => $this->comment->id,
                'type' => 'new_comment'
            ]
        ];
    }
}