<?php

namespace App\Policies;

use App\Models\Course;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class CoursePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): Response
    {
        return $user->hasPermissionTo('read_course')
            ? Response::allow()
            : Response::deny('You do not have permission to view courses.');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Course $course): Response
    {
        return $user->hasPermissionTo('read_course')
            ? Response::allow()
            : Response::deny('You do not have permission to view this course.');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): Response
    {
        return $user->hasPermissionTo('create_course')
            ? Response::allow()
            : Response::deny('You do not have permission to create courses.');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Course $course): Response
    {
        return $user->hasPermissionTo('update_course')
            ? Response::allow()
            : Response::deny('You do not have permission to update this course.');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Course $course): Response
    {
        return $user->hasPermissionTo('delete_course')
            ? Response::allow()
            : Response::deny('You do not have permission to delete this course.');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Course $course): Response
    {
        return $user->hasPermissionTo('delete_course')
            ? Response::allow()
            : Response::deny('You do not have permission to restore this course.');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Course $course): Response
    {
        return $user->hasPermissionTo('delete_course')
            ? Response::allow()
            : Response::deny('You do not have permission to permanently delete this course.');
    }
}
