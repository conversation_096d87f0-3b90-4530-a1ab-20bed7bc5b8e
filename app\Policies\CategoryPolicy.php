<?php

namespace App\Policies;

use App\Models\Category;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class CategoryPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): Response
    {
        return $user->hasPermissionTo('read_category')
            ? Response::allow()
            : Response::deny('You do not have permission to view categories.');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Category $category): Response
    {
        return $user->hasPermissionTo('read_category')
            ? Response::allow()
            : Response::deny('You do not have permission to view this category.');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): Response
    {
        return $user->hasPermissionTo('create_category')
            ? Response::allow()
            : Response::deny('You do not have permission to create categories.');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Category $category): Response
    {
        return $user->hasPermissionTo('update_category')
            ? Response::allow()
            : Response::deny('You do not have permission to update this category.');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Category $category): Response
    {
        return $user->hasPermissionTo('delete_category')
            ? Response::allow()
            : Response::deny('You do not have permission to delete this category.');
    }


    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Category $category): Response
    {
        return $user->hasPermissionTo('restore_category')
            ? Response::allow()
            : Response::deny('You do not have permission to restore this category.');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Category $category): Response
    {
        return $user->hasPermissionTo('force_delete_category')
            ? Response::allow()
            : Response::deny('You do not have permission to permanently delete this category.');
    }
}
