<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CourseWizardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Course fields
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'objectives' => ['nullable', 'array'],
            'objectives.*' => ['string', 'max:500'],
            'category_id' => ['required', 'exists:categories,id'],
            'sub_category_id' => ['nullable', 'exists:sub_categories,id'],
            'duration' => ['nullable', 'integer'],
            'image' => ['nullable', 'file', 'mimes:jpg,jpeg,png,gif,mp4,mov', 'max:10240'], // 10MB max
            'video' => ['nullable', 'file', 'mimes:mp4,mov,avi', 'max:51200'], // 50MB max

            'welcome_message' => ['nullable', 'string'],
            'congratulations_message' => ['nullable', 'string'],
            'instructor_name' => ['nullable', 'string', 'max:255'],
            'instructor_description' => ['nullable', 'string'],
            'instructor_facebook' => ['nullable', 'url'],
            'instructor_instagram' => ['nullable', 'url'],
            'instructor_twitter' => ['nullable', 'url'],

            'levels' => ['nullable', 'array'],
            'levels.*' => ['exists:levels,id'],

            // Sections
            'sections' => ['nullable', 'array'],
            'sections.*.title' => ['required', 'string', 'max:255'],
            'sections.*.description' => ['nullable', 'string'],
            'sections.*.order' => [
                'required',
                'integer',
                'distinct'
            ],

            // Section Quizzes
            'sections.*.quizzes' => ['nullable', 'array'],
            'sections.*.quizzes.*.question' => ['required', 'string'],
            'sections.*.quizzes.*.choice_a' => ['required', 'string'],
            'sections.*.quizzes.*.choice_b' => ['required', 'string'],
            'sections.*.quizzes.*.choice_c' => ['required', 'string'],
            'sections.*.quizzes.*.choice_d' => ['required', 'string'],
            'sections.*.quizzes.*.correct_answer' => ['required', 'in:a,b,c,d'],

            // Lectures
            'sections.*.lectures' => ['nullable', 'array'],
            'sections.*.lectures.*.title' => ['required', 'string', 'max:255'],
            'sections.*.lectures.*.description' => ['nullable', 'string'],
            'sections.*.lectures.*.order' => [
                'required',
                'integer',
                'distinct'
            ],
            'sections.*.lectures.*.duration' => ['nullable', 'integer'],
            'sections.*.lectures.*.lecture_note' => ['nullable', 'string'],

            // Course materials
            'materials' => ['nullable', 'array'],
            'materials.*.title' => ['nullable', 'string', 'max:255'],
            'materials.*.description' => ['nullable', 'string'],
            'materials.*.file' => ['nullable', 'file', 'mimes:pdf,doc,docx,ppt,pptx,zip', 'max:51200'],
        ];
    }

    /**
     * Customize messages if needed
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Course title is required.',
            'sections.*.lectures.*.title.required' => 'Each lecture must have a title.',
            'sections.*.lectures.*.duration.required' => 'Each lecture must have a duration.',
            'sections.*.lectures.*.video_file.mimes' => 'Lecture video must be mp4, mov, avi, or wmv.',
            'sections.*.lectures.*.image_file.image' => 'Lecture image must be an image file.',
            'sections.*.lectures.*.quizzes.*.correct_answer.in' => 'The correct answer must be one of: a, b, c, d.',
            'materials.*.file.mimes' => 'Course material must be a pdf, doc, docx, ppt, pptx, or zip file.',
            'sections.*.order.distinct' => 'Section order must be unique.',
            'sections.*.lectures.*.order.distinct' => 'Lecture order must be unique.',
        ];
    }
}

