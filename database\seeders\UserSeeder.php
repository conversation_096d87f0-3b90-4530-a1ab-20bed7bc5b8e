<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $admin = User::updateOrCreate([
            'email' => '<EMAIL>',
            'phone_number' => '251900000000',
            'username' => 'admin',
        ], [
            'full_name' => 'Admin',
            'verified_at' => now(),
            'password' => bcrypt('123456'),
            'status' => true
        ]);

        $admin->assignRole('Admin');

        $student = User::updateOrCreate([
            'email' => '<EMAIL>',
            'phone_number' => '251911111111',
            'username' => 'student',
        ], [
            'full_name' => 'Student',
            'verified_at' => now(),
            'password' => bcrypt('123456'),
            'status' => true
        ]);

        $student->assignRole('Student');
    }
}
