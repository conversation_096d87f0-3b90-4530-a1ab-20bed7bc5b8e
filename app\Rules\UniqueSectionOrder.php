<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\Section;

class UniqueSectionOrder implements ValidationRule
{
    private $courseId;
    private $excludeId;

    public function __construct($courseId = null, $excludeId = null)
    {
        $this->courseId = $courseId;
        $this->excludeId = $excludeId;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $courseId = $this->courseId ?? request()->input('course_id');

        if (!$courseId) {
            return;
        }

        // Check if order already exists in database for this course
        $query = Section::where('course_id', $courseId)
            ->where('order', $value);

        if ($this->excludeId) {
            $query->where('id', '!=', $this->excludeId);
        }

        $exists = $query->exists();

        if ($exists) {
            $fail('A section with this order already exists for this course.');
        }

        // For wizard requests, also check within current request data
        if (request()->has('sections')) {
            $sections = request()->input('sections', []);
            $orders = collect($sections)->pluck('order')->filter()->toArray();

            if (count($orders) !== count(array_unique($orders))) {
                $fail('Section orders must be unique.');
            }
        }
    }
}
