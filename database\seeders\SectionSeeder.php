<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\Level;
use App\Models\Section;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Seeder;

class SectionSeeder extends Seeder
{
    use HasFactory;
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // randomaly take course ids from courses table and level ids from levels table
        $courseIds = Course::pluck('id')->toArray();

        // Create 10 sections with random course_id and level_id
        Section::factory()
            ->count(10)
            ->state(new Sequence(
                fn($sequence) => [
                    'order' => $sequence->index + 1,
                    'course_id' => $courseIds[array_rand($courseIds)],
                ]
            ))->create();
    }
}
